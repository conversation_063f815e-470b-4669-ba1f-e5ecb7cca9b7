#!/usr/bin/env python3
"""
语法检查脚本
"""

import ast
import sys
from pathlib import Path

def check_syntax(file_path):
    """检查Python文件的语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析AST
        ast.parse(source)
        print(f"✅ {file_path}: 语法正确")
        return True
        
    except SyntaxError as e:
        print(f"❌ {file_path}: 语法错误")
        print(f"   行 {e.lineno}: {e.text.strip() if e.text else ''}")
        print(f"   错误: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {file_path}: 其他错误 - {e}")
        return False

def main():
    """主函数"""
    files_to_check = [
        "table_render/main_generator.py",
        "table_render/renderers/html_renderer.py", 
        "table_render/postprocessors/image_augmentor.py",
        "table_render/utils/performance_profiler.py",
        "run_performance_test.py"
    ]
    
    print("🔍 检查语法...")
    all_good = True
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            if not check_syntax(file_path):
                all_good = False
        else:
            print(f"⚠️  {file_path}: 文件不存在")
    
    if all_good:
        print("\n🎉 所有文件语法检查通过!")
        return 0
    else:
        print("\n❌ 发现语法错误，请修复后重试")
        return 1

if __name__ == "__main__":
    sys.exit(main())
